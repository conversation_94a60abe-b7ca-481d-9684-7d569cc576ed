Resources
=========

Examples
--------

For recipes and patterns, see our examples:

* `parallel examples <https://github.com/NVIDIA/cccl/tree/main/python/cuda_cccl/tests/parallel/examples>`_
* `cooperative examples <https://github.com/NVIDIA/cccl/tree/main/python/cuda_cccl/tests/cooperative/examples>`_

CUB and Thrust Documentation
----------------------------

The CCCL Python libraries are built on top of the CUB and Thrust libraries.
See the `CUB documentation <https://nvlabs.github.io/cub/>`_ and `Thrust documentation <https://thrust.github.io/>`_
for more information regarding the underlying libraries.


Asking for Help
---------------

If you have a question, run into an issue, or have a feature request,
please raise an issue or start a discussion on our `GitHub repository <https://github.com/NVIDIA/cccl/issues>`_.

Contributing
------------

We welcome contributions! Please see the
`contributing guide <https://github.com/NVIDIA/cccl/blob/main/CONTRIBUTING.md>`_
for instructions on how to set up a development environment and submit a pull request.

Once you have a development environment set up, see :doc:`setup` for instructions
on how to install `cuda.cccl` in development mode.

License
-------

The CCCL Python libraries are licensed under the `Apache License 2.0 <https://www.apache.org/licenses/LICENSE-2.0>`_.
