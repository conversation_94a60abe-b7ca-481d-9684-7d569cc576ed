..
   This file was automatically generated. Do not edit.

bmsk.clamp.b32
^^^^^^^^^^^^^^
.. code-block:: cuda

   // bmsk.clamp.b32 dest, a_reg, b_reg; // PTX ISA 76, SM_70
   template <typename = void>
   __device__ static inline uint32_t bmsk_clamp(
     uint32_t a_reg,
     uint32_t b_reg);

bmsk.wrap.b32
^^^^^^^^^^^^^
.. code-block:: cuda

   // bmsk.wrap.b32 dest, a_reg, b_reg; // PTX ISA 76, SM_70
   template <typename = void>
   __device__ static inline uint32_t bmsk_wrap(
     uint32_t a_reg,
     uint32_t b_reg);
