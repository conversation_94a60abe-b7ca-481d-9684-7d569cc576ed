..
   This file was automatically generated. Do not edit.

tcgen05.shift.cta_group::1.down
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.. code-block:: cuda

   // tcgen05.shift.cta_group.down [taddr]; // PTX ISA 86, SM_100a, SM_103a, SM_110a
   // .cta_group = { .cta_group::1, .cta_group::2 }
   template <cuda::ptx::dot_cta_group Cta_Group>
   __device__ static inline void tcgen05_shift_down(
     cuda::ptx::cta_group_t<Cta_Group> cta_group,
     uint32_t taddr);

tcgen05.shift.cta_group::2.down
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.. code-block:: cuda

   // tcgen05.shift.cta_group.down [taddr]; // PTX ISA 86, SM_100a, SM_103a, SM_110a
   // .cta_group = { .cta_group::1, .cta_group::2 }
   template <cuda::ptx::dot_cta_group Cta_Group>
   __device__ static inline void tcgen05_shift_down(
     cuda::ptx::cta_group_t<Cta_Group> cta_group,
     uint32_t taddr);
