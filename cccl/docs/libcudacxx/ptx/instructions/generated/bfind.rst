..
   This file was automatically generated. Do not edit.

bfind.u32
^^^^^^^^^
.. code-block:: cuda

   // bfind.u32 dest, a_reg; // PTX ISA 20, SM_50
   template <typename = void>
   __device__ static inline uint32_t bfind(
     uint32_t a_reg);

bfind.shiftamt.u32
^^^^^^^^^^^^^^^^^^
.. code-block:: cuda

   // bfind.shiftamt.u32 dest, a_reg; // PTX ISA 20, SM_50
   template <typename = void>
   __device__ static inline uint32_t bfind_shiftamt(
     uint32_t a_reg);

bfind.u64
^^^^^^^^^
.. code-block:: cuda

   // bfind.u64 dest, a_reg; // PTX ISA 20, SM_50
   template <typename = void>
   __device__ static inline uint32_t bfind(
     uint64_t a_reg);

bfind.shiftamt.u64
^^^^^^^^^^^^^^^^^^
.. code-block:: cuda

   // bfind.shiftamt.u64 dest, a_reg; // PTX ISA 20, SM_50
   template <typename = void>
   __device__ static inline uint32_t bfind_shiftamt(
     uint64_t a_reg);

bfind.s32
^^^^^^^^^
.. code-block:: cuda

   // bfind.s32 dest, a_reg; // PTX ISA 20, SM_50
   template <typename = void>
   __device__ static inline uint32_t bfind(
     int32_t a_reg);

bfind.shiftamt.s32
^^^^^^^^^^^^^^^^^^
.. code-block:: cuda

   // bfind.shiftamt.s32 dest, a_reg; // PTX ISA 20, SM_50
   template <typename = void>
   __device__ static inline uint32_t bfind_shiftamt(
     int32_t a_reg);

bfind.s64
^^^^^^^^^
.. code-block:: cuda

   // bfind.s64 dest, a_reg; // PTX ISA 20, SM_50
   template <typename = void>
   __device__ static inline uint32_t bfind(
     int64_t a_reg);

bfind.shiftamt.s64
^^^^^^^^^^^^^^^^^^
.. code-block:: cuda

   // bfind.shiftamt.s64 dest, a_reg; // PTX ISA 20, SM_50
   template <typename = void>
   __device__ static inline uint32_t bfind_shiftamt(
     int64_t a_reg);
