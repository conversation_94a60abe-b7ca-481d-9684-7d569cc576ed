..
   This file was automatically generated. Do not edit.

tcgen05.fence::before_thread_sync
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.. code-block:: cuda

   // tcgen05.fence::before_thread_sync; // PTX ISA 86, SM_100a, SM_100f, SM_103a, SM_103f, SM_110a, SM_110f
   template <typename = void>
   __device__ static inline void tcgen05_fence_before_thread_sync();

tcgen05.fence::after_thread_sync
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.. code-block:: cuda

   // tcgen05.fence::after_thread_sync; // PTX ISA 86, SM_100a, SM_100f, SM_103a, SM_103f, SM_110a, SM_110f
   template <typename = void>
   __device__ static inline void tcgen05_fence_after_thread_sync();
