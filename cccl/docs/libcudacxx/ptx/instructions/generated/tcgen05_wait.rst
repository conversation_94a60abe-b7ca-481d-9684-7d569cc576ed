..
   This file was automatically generated. Do not edit.

tcgen05.wait::ld.sync.aligned
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.. code-block:: cuda

   // tcgen05.wait::ld.sync.aligned; // PTX ISA 86, SM_100a, SM_100f, SM_103a, SM_103f, SM_110a, SM_110f
   template <typename = void>
   __device__ static inline void tcgen05_wait_ld();

tcgen05.wait::st.sync.aligned
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
.. code-block:: cuda

   // tcgen05.wait::st.sync.aligned; // PTX ISA 86, SM_100a, SM_100f, SM_103a, SM_103f, SM_110a, SM_110f
   template <typename = void>
   __device__ static inline void tcgen05_wait_st();
