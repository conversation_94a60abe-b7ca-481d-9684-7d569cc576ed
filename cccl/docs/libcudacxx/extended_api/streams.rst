.. _libcudacxx-extended-api-streams:

Streams
=======

.. toctree::
   :hidden:
   :maxdepth: 1

   streams/stream_ref

.. list-table::
   :widths: 25 45 30 30
   :header-rows: 1

   * - **Header**
     - **Content**
     - **CCCL Availability**
     - **CUDA Toolkit Availability**

   * - :ref:`stream_ref <libcudacxx-extended-api-streams-stream-ref>`
     - A wrapper around a ``cudaStream_t``
     - CCCL 2.2.0
     - CUDA 12.3
