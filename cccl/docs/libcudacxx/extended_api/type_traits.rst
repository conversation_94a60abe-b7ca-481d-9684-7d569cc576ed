.. _libcudacxx-extended-api-type_traits:

Type traits
-----------

.. toctree::
   :hidden:
   :maxdepth: 1

   type_traits/is_floating_point

.. list-table::
   :widths: 25 45 30 30
   :header-rows: 1

   * - **Header**
     - **Content**
     - **CCCL Availability**
     - **CUDA Toolkit Availability**

   * - :ref:`cuda::is_floating_point <libcudacxx-extended-api-type_traits-is_floating_point>`
     - Tells whether a type is a floating point type
     - CCCL 3.0.0
     - CUDA 13.0
