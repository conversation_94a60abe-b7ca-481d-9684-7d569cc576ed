.. _libcudacxx-extended-api-utility:

Utility
=======

.. toctree::
   :hidden:
   :maxdepth: 1

   cuda::static_for <utility/static_for>

.. list-table::
   :widths: 25 45 30 30
   :header-rows: 1

   * - **Header**
     - **Content**
     - **CCCL Availability**
     - **CUDA Toolkit Availability**

   * - :ref:`static_for <libcudacxx-extended-api-utility-static-for>`
     - Compile-time ``for`` loop
     - CCCL 3.1.0
     - CUDA 13.1
