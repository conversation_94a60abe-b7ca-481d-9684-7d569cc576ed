.. _cccl-contributing:

Contributing to the CUDA Core Compute Libraries
===============================================

.. toctree::
   :maxdepth: 1

   contributing/code_of_conduct

We welcome contributions - just send us a pull request!
You can find detailed instructions on `GitHub <https://github.com/NVIDIA/cccl/blob/main/CONTRIBUTING.md>`_.

libcu++ uses the `Apache License v2.0 with LLVM Exceptions <https://llvm.org/LICENSE.txt>`_.
