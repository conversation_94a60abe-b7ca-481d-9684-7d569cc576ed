//===----------------------------------------------------------------------===//
//
// Part of CUDA Experimental in CUDA C++ Core Libraries,
// under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
// SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES.
//
//===----------------------------------------------------------------------===//

#ifndef __CUDAX_VERSION__
#define __CUDAX_VERSION__

#include <cuda/std/version>

#define CUDAX_VERSION       CCCL_VERSION
#define CUDAX_VERSION_MAJOR CCCL_MAJOR_VERSION
#define CUDAX_VERSION_MINOR CCCL_MINOR_VERSION
#define CUDAX_VERSION_PATCH CCCL_PATCH_VERSION

#endif // __CUDAX_VERSION__
