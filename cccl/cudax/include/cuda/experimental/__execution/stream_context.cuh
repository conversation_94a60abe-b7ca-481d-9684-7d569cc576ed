//===----------------------------------------------------------------------===//
//
// Part of CUDA Experimental in CUDA C++ Core Libraries,
// under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
// SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES.
//
//===----------------------------------------------------------------------===//

#ifndef __CUDAX_EXECUTION_STREAM_CONTEXT
#define __CUDAX_EXECUTION_STREAM_CONTEXT

#include <cuda/std/detail/__config>

#if defined(_CCCL_IMPLICIT_SYSTEM_HEADER_GCC)
#  pragma GCC system_header
#elif defined(_CCCL_IMPLICIT_SYSTEM_HEADER_CLANG)
#  pragma clang system_header
#elif defined(_CCCL_IMPLICIT_SYSTEM_HEADER_MSVC)
#  pragma system_header
#endif // no system header

// IWYU pragma: begin_exports
#include <cuda/experimental/__execution/stream/adaptor.cuh>
#include <cuda/experimental/__execution/stream/bulk.cuh>
#include <cuda/experimental/__execution/stream/context.cuh>
#include <cuda/experimental/__execution/stream/continues_on.cuh>
#include <cuda/experimental/__execution/stream/domain.cuh>
#include <cuda/experimental/__execution/stream/launch.cuh>
#include <cuda/experimental/__execution/stream/let_value.cuh>
#include <cuda/experimental/__execution/stream/sequence.cuh>
#include <cuda/experimental/__execution/stream/starts_on.cuh>
#include <cuda/experimental/__execution/stream/sync_wait.cuh>
// IWYU pragma: end_exports

#endif //__CUDAX_EXECUTION_STREAM_CONTEXT
