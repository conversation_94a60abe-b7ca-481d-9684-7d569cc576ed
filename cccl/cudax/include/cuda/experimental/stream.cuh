//===----------------------------------------------------------------------===//
//
// Part of CUDA Experimental in CUDA C++ Core Libraries,
// under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
// SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES.
//
//===----------------------------------------------------------------------===//

#ifndef __CUDAX_STREAM__
#define __CUDAX_STREAM__

#include <cuda/experimental/__execution/stream/scheduler.cuh>
#include <cuda/experimental/__stream/stream.cuh>

#endif // __CUDAX_STREAM__
